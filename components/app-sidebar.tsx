'use client';

import type { User } from 'next-auth';
import { useRouter, usePathname } from 'next/navigation';

import { PlusIcon } from '@/components/icons';
import { SidebarHistory } from '@/components/sidebar-history';
import { SidebarUserNav } from '@/components/sidebar-user-nav';
import { SidebarJournal } from '@/components/sidebar-journal';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  useSidebar,
} from '@/components/ui/sidebar';
import Link from 'next/link';

export function AppSidebar({ user }: { user: User | undefined }) {
  const router = useRouter();
  const { setOpenMobile } = useSidebar();
  const pathname = usePathname();
  const isJournalPage = pathname?.startsWith('/journal');
  const href = isJournalPage ? '/journal' : '/';

  return (
    <Sidebar className="group-data-[side=left]:border-r-0 bg-sidebar">
      <SidebarHeader>
        <Link
          href={href}
          onClick={() => {
            setOpenMobile(false);
          }}
          className="flex flex-row items-center"
        >
          <SidebarMenu className="gradient-primary text-white rounded shadow-md p-2">
            <div className="flex flex-row justify-between items-center">
              <PlusIcon />
              {isJournalPage ? (
                <span className="text-sm font-semibold px-2 hover:bg-muted rounded-md cursor-pointer">
                  New Journal
                </span>
              ) : (
                <span className="text-sm font-semibold px-2 hover:bg-muted rounded-md cursor-pointer">
                  New Conversation
                </span>
              )}
            </div>
          </SidebarMenu>
        </Link>
      </SidebarHeader>
      <SidebarContent>
        {isJournalPage ? (
          <SidebarJournal user={user} />
        ) : (
          <SidebarHistory user={user} />
        )}
      </SidebarContent>
      <SidebarFooter isLoggedIn={!!user}>
        {user && <SidebarUserNav user={user} />}
      </SidebarFooter>
    </Sidebar>
  );
}
