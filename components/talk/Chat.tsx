'use client';

import { VoiceProvider } from '@humeai/voice-react';
import Messages from './Messages';
import Controls from './Controls';
import StartCall from './StartCall';
import { type ComponentRef, useRef, createContext, useState } from 'react';
import { generateUUID } from '@/lib/utils';

// Update the context to include the createNewChat function and chatId
export const MessageContext = createContext<{
  saveMessage: (
    text: string,
    role: 'user' | 'assistant',
  ) => Promise<string | undefined>;
  createNewChat: () => Promise<string | undefined>;
  chatId: string | null;
}>({
  saveMessage: async () => undefined,
  createNewChat: async () => undefined,
  chatId: null,
});

export default function ClientComponent({
  accessToken,
  userId,
}: {
  accessToken: string;
  userId: string;
}) {
  const timeout = useRef<number | null>(null);
  const ref = useRef<ComponentRef<typeof Messages> | null>(null);
  const [chatId, setChatId] = useState<string | null>(null);
  const [processedMessageIds] = useState(new Set<string>());

  // Function to save messages that can be passed to children
  const saveMessage = async (content: string, role: 'user' | 'assistant') => {
    if (!chatId) {
      console.error('Cannot save message: No active chat');
      return;
    }

    const messageId = await saveMessageToDatabase({
      chatId,
      userId,
      role,
      content,
    });

    return messageId;
  };

  // Function to create a new chat
  const createNewChat = async () => {
    try {
      const newChatId = generateUUID();
      const response = await fetch('/talk/api/create-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatId: newChatId,
          userId,
          title: 'Voice Chat',
          isVoiceChat: true,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create chat');
      }

      setChatId(newChatId);
      return newChatId;
    } catch (error) {
      throw error;
    }
  };

  return (
    <div className="relative grow flex flex-col mx-auto size-full overflow-hidden">
      <MessageContext.Provider value={{ saveMessage, createNewChat, chatId }}>
        <VoiceProvider
          auth={{ type: 'accessToken', value: accessToken }}
          configId="b2e52f49-1614-426e-b3c5-2154d015d1b9"
          onMessage={async (message) => {
            // Extract content using type assertion to bypass TypeScript checks
            const msg = message as any;
            // Only process messages if we have an active chat and it's a valid message type
            if (
              !chatId ||
              (msg.type !== 'user_message' && msg.type !== 'assistant_message')
            )
              return;

            // Only process final/complete messages
            if (msg.interim === true) {
              return; // Skip incomplete/interim messages
            }

            // Check for duplicate messages
            const messageId = msg.message?.id || msg.id;
            if (messageId && processedMessageIds.has(messageId)) {
              return; // Skip if we've already processed this message
            }

            let messageContent = 'No content available';
            let role = 'user';

            // Try to extract content from various possible properties
            if (
              typeof msg === 'object' &&
              msg !== null &&
              (msg.type === 'user_message' || msg.type === 'assistant_message')
            ) {
              messageContent = msg.message.content;
              role = msg.message.role;
            }

            processedMessageIds.add(messageId);

            // Save message and get the messageId
            const savedMessageId = await saveMessage(
              messageContent,
              role === 'user' ? 'user' : 'assistant',
            );

            // Process emotions if available
            if (
              msg.type === 'user_message' &&
              msg.models?.prosody?.scores &&
              savedMessageId
            ) {
              const scores = msg.models.prosody.scores;
              if (Object.keys(scores).length > 0) {
                fetch('/talk/api/emotion', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    messageId: savedMessageId,
                    chatId,
                    emotions: scores,
                  }),
                }).catch(() => {});
              }
            }

            if (timeout.current) {
              window.clearTimeout(timeout.current);
            }

            timeout.current = window.setTimeout(() => {
              if (ref.current) {
                const scrollHeight = ref.current.scrollHeight;

                ref.current.scrollTo({
                  top: scrollHeight,
                  behavior: 'smooth',
                });
              }
            }, 200);
          }}
        >
          <Messages ref={ref} />
          <Controls />
          <StartCall />
        </VoiceProvider>
      </MessageContext.Provider>
    </div>
  );
}

async function saveMessageToDatabase({
  chatId,
  userId,
  role,
  content,
}: {
  chatId: string;
  userId: string;
  role: 'user' | 'assistant';
  content: string;
}) {
  try {
    const messageId = generateUUID();

    await fetch('/talk/api/message', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: messageId,
        chatId,
        role,
        content,
        userId,
      }),
    });

    return messageId;
  } catch (error) {
    throw error;
  }
}
