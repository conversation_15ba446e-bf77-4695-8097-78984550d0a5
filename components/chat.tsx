'use client';

import type { Attachment, Message } from 'ai';
import { useChat } from 'ai/react';
import { AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef, useCallback } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { useWindowSize } from 'usehooks-ts';

import { ChatHeader } from '@/components/chat-header';
import type { Vote } from '@/lib/db/schema';
import { fetcher } from '@/lib/utils';

import { Block, type UIBlock } from './block';
import { BlockStreamHandler } from './block-stream-handler';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import type { VisibilityType } from './visibility-selector';

export function Chat({
  id,
  initialMessages,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  isUserTemporary,
}: {
  id: string;
  initialMessages: Array<Message>;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  isUserTemporary: boolean;
}) {
  const { mutate } = useSWRConfig();

  const [offset, setOffset] = useState(initialMessages.length);
  const [loadingMore, setLoadingMore] = useState(false);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Use a consistent single chat ID for all conversations
  const singleChatId = 'single-conversation';

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    isLoading,
    stop,
    reload,
    data: streamingData,
  } = useChat({
    id: singleChatId,
    body: { id: singleChatId, modelId: selectedModelId },
    initialMessages,
    onFinish: () => {
      // No need to mutate history since we have a single conversation
    },
  });

  const loadMoreMessages = useCallback(async () => {
    if (loadingMore) return;

    setLoadingMore(true);
    try {
      const container = messagesContainerRef.current;
      if (!container) return;

      // Store the current scroll height before loading new messages
      const prevScrollHeight = container.scrollHeight;

      const response = await fetch(
        `/api/all-messages?limit=200&offset=${messages.length}`,
      );
      if (!response.ok) {
        throw new Error('Failed to fetch more messages');
      }
      const newMessages: Message[] = await response.json();

      if (newMessages.length > 0) {
        setMessages((prevMessages) => {
          // Create a Set of existing message IDs for quick lookup
          const existingIds = new Set(prevMessages.map((msg) => msg.id));
          // Filter out any duplicate messages from newMessages
          const uniqueNewMessages = newMessages.filter(
            (msg) => !existingIds.has(msg.id),
          );
          // Add new messages at the beginning since they're older
          return [...uniqueNewMessages, ...prevMessages];
        });

        // After the new messages are rendered, adjust scroll position
        requestAnimationFrame(() => {
          if (container) {
            const newScrollHeight = container.scrollHeight;
            const addedHeight = newScrollHeight - prevScrollHeight;
            container.scrollTop = addedHeight;
          }
        });
      }
    } catch (error) {
      console.error('Error loading more messages:', error);
    } finally {
      setLoadingMore(false);
    }
  }, [
    loadingMore,
    setLoadingMore,
    messagesContainerRef,
    messages,
    setMessages,
  ]); // Restored dependency array for useCallback

  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      if (container.scrollTop === 0 && !loadingMore) {
        loadMoreMessages();
      }
    };

    container.addEventListener('scroll', handleScroll);

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [offset, loadingMore, setMessages, loadMoreMessages]); // Add dependencies

  const { width: windowWidth = 1920, height: windowHeight = 1080 } =
    useWindowSize();

  const [block, setBlock] = useState<UIBlock>({
    documentId: 'init',
    content: '',
    title: '',
    status: 'idle',
    isVisible: false,
    boundingBox: {
      top: windowHeight / 4,
      left: windowWidth / 4,
      width: 250,
      height: 50,
    },
  });

  const { data: votes } = useSWR<Array<Vote>>(
    `/api/vote?chatId=${singleChatId}`,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 60000, // Only refetch at most every 60 seconds
    },
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          chatId={singleChatId}
          selectedModelId={selectedModelId}
          selectedVisibilityType={selectedVisibilityType}
          isReadonly={isReadonly}
          append={append}
        />

        <div className="flex-1 overflow-y-auto" ref={messagesContainerRef}>
          {loadingMore && (
            <div className="flex justify-center py-2 text-gray-500">
              Loading more messages...
            </div>
          )}
          <Messages
            chatId={singleChatId}
            block={block}
            setBlock={setBlock}
            isLoading={isLoading}
            votes={votes}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            isReadonly={isReadonly}
            isUserTemporary={isUserTemporary}
          />
        </div>

        <div className="shrink-0 bg-white">
          <form className="flex mx-auto px-4 pt-6 pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
            {!isReadonly && (
              <MultimodalInput
                chatId={singleChatId}
                input={input}
                setInput={setInput}
                handleSubmit={handleSubmit}
                isLoading={isLoading}
                stop={stop}
                attachments={attachments}
                setAttachments={setAttachments}
                messages={messages}
                setMessages={setMessages}
                append={append}
              />
            )}
          </form>
        </div>
      </div>

      <AnimatePresence>
        {block?.isVisible && (
          <Block
            chatId={singleChatId}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            isLoading={isLoading}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            append={append}
            block={block}
            setBlock={setBlock}
            messages={messages}
            setMessages={setMessages}
            reload={reload}
            votes={votes}
            isReadonly={isReadonly}
          />
        )}
      </AnimatePresence>

      <BlockStreamHandler streamingData={streamingData} setBlock={setBlock} />
    </>
  );
}
