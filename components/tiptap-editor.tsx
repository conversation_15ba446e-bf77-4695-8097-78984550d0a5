'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useEffect, forwardRef, useState } from 'react';
import { Button } from './ui/button';
import { 
  Bold, 
  Italic, 
  List, 
  ListOrdered, 
  Heading1, 
  Heading2, 
  Undo, 
  Redo,
  Quote,
  Code,
  MinusIcon,
  FileText,
  Eye
} from 'lucide-react';
import { Markdown } from 'tiptap-markdown';

interface TiptapEditorProps {
  content: string;
  onChange: (content: string) => void;
  disabled?: boolean;
  className?: string;
  autofocus?: boolean;
}

const MenuBar = ({ editor, showMarkdown, setShowMarkdown }: { 
  editor: any; 
  showMarkdown: boolean;
  setShowMarkdown: (show: boolean) => void;
}) => {
  if (!editor) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-1 p-2 border-b bg-muted/20 rounded-t-md">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().toggleBold().run()}
        className={editor.isActive('bold') ? 'bg-muted' : ''}
        title="Bold"
        type="button"
        disabled={showMarkdown}
      >
        <Bold className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().toggleItalic().run()}
        className={editor.isActive('italic') ? 'bg-muted' : ''}
        title="Italic"
        type="button"
        disabled={showMarkdown}
      >
        <Italic className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-1" />
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
        className={editor.isActive('heading', { level: 1 }) ? 'bg-muted' : ''}
        title="Heading 1"
        type="button"
        disabled={showMarkdown}
      >
        <Heading1 className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
        className={editor.isActive('heading', { level: 2 }) ? 'bg-muted' : ''}
        title="Heading 2"
        type="button"
        disabled={showMarkdown}
      >
        <Heading2 className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-1" />
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        className={editor.isActive('bulletList') ? 'bg-muted' : ''}
        title="Bullet List"
        type="button"
        disabled={showMarkdown}
      >
        <List className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().toggleOrderedList().run()}
        className={editor.isActive('orderedList') ? 'bg-muted' : ''}
        title="Ordered List"
        type="button"
        disabled={showMarkdown}
      >
        <ListOrdered className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-1" />
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().toggleBlockquote().run()}
        className={editor.isActive('blockquote') ? 'bg-muted' : ''}
        title="Quote"
        type="button"
        disabled={showMarkdown}
      >
        <Quote className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().toggleCodeBlock().run()}
        className={editor.isActive('codeBlock') ? 'bg-muted' : ''}
        title="Code Block"
        type="button"
        disabled={showMarkdown}
      >
        <Code className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().setHorizontalRule().run()}
        title="Horizontal Rule"
        type="button"
        disabled={showMarkdown}
      >
        <MinusIcon className="h-4 w-4" />
      </Button>
      <div className="w-px h-6 bg-border mx-1" />
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().undo().run()}
        disabled={!editor.can().undo() || showMarkdown}
        title="Undo"
        type="button"
      >
        <Undo className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => editor.chain().focus().redo().run()}
        disabled={!editor.can().redo() || showMarkdown}
        title="Redo"
        type="button"
      >
        <Redo className="h-4 w-4" />
      </Button>
      <div className="flex-1"></div>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowMarkdown(!showMarkdown)}
        className={showMarkdown ? 'bg-muted' : ''}
        title={showMarkdown ? "Visual Editor" : "Markdown"}
        type="button"
      >
        {showMarkdown ? <Eye className="h-4 w-4 mr-1" /> : <FileText className="h-4 w-4 mr-1" />}
        {showMarkdown ? "Visual" : "Markdown"}
      </Button>
    </div>
  );
};

const TiptapEditor = forwardRef<HTMLDivElement, TiptapEditorProps>(
  ({ content, onChange, disabled = false, className = '', autofocus = false }, ref) => {
    const [showMarkdown, setShowMarkdown] = useState(false);
    const [markdownContent, setMarkdownContent] = useState('');
    
    const editor = useEditor({
      extensions: [
        StarterKit,
        Markdown.configure({
          html: false,
          tightLists: true,
          tightListClass: 'tight',
          bulletListMarker: '-',
          linkify: true,
        }),
      ],
      content: content || '',
      editable: !disabled && !showMarkdown,
      onUpdate: ({ editor }) => {
        const markdown = editor.storage.markdown.getMarkdown();
        setMarkdownContent(markdown);
        onChange(markdown);
      },
      editorProps: {
        attributes: {
          class: 'prose prose-sm sm:prose max-w-none focus:outline-none min-h-[300px] sm:min-h-[500px] p-4',
        },
      },
    });

    // Update editor content when content prop changes
    useEffect(() => {
      if (editor && content !== markdownContent) {
        editor.commands.setContent(content);
        setMarkdownContent(content);
      }
    }, [editor, content]);

    useEffect(() => {
      if (editor && autofocus) {
        editor.commands.focus('end');
      }
    }, [editor, autofocus]);

    const handleMarkdownChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newMarkdown = e.target.value;
      setMarkdownContent(newMarkdown);
      onChange(newMarkdown);
      
      // Update the editor content when switching back to visual mode
      if (editor) {
        editor.commands.setContent(newMarkdown);
      }
    };

    return (
      <div className={`border rounded-md overflow-hidden ${className}`} ref={ref}>
        <MenuBar editor={editor} showMarkdown={showMarkdown} setShowMarkdown={setShowMarkdown} />
        
        {showMarkdown ? (
          <textarea
            className="w-full h-full min-h-[300px] sm:min-h-[500px] p-4 border-none focus:outline-none focus:ring-0 resize-none font-mono text-sm"
            value={markdownContent}
            onChange={handleMarkdownChange}
            disabled={disabled}
            placeholder="Write your journal entry in Markdown..."
            style={{ fontSize: '16px' }}
          />
        ) : (
          <EditorContent editor={editor} className="overflow-y-auto" />
        )}
      </div>
    );
  }
);

TiptapEditor.displayName = 'TiptapEditor';

export default TiptapEditor;
