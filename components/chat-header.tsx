'use client';
import { useRouter } from 'next/navigation';
import { useWindowSize } from 'usehooks-ts';
import type { ChatRequestOptions, CreateMessage, Message } from 'ai';
import { SidebarToggle } from '@/components/sidebar-toggle';
import { Button } from '@/components/ui/button';
import { useSidebar } from './ui/sidebar';
import { memo } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import type { VisibilityType, } from './visibility-selector';
import { Mic } from 'lucide-react';

function PureChatHeader({
  chatId,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  append,
}: {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  append?: (
    message: Message | CreateMessage,
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
}) {
  const router = useRouter();
  const { open } = useSidebar();

  const { width: windowWidth } = useWindowSize();

  return (
    <header className="chat-header">
      <SidebarToggle />
      <div className="flex flex-col items-center flex-1 justify-center">
        <span
          className="chat-header-title"
          onClick={() => {
            router.push('/');
            router.refresh();
          }}
        >
          GentleGossip
        </span>
      </div>
      {append && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className="chat-header-button"
              onClick={() => {
                router.push('/journal');
                router.refresh();
              }}
            >
              <span>Journal</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>New Journal</TooltipContent>
        </Tooltip>
      )}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            className="chat-header-button"
            onClick={() => {
              router.push('/talk');
              router.refresh();
            }}
          >
            <Mic className="mr-2 size-4" />
            <span>Voice</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>Voice Chat</TooltipContent>
      </Tooltip>

      {/* {!isReadonly && (
        <ModelSelector
          selectedModelId={selectedModelId}
          className="order-1 md:order-2"
        />
      )} */}

      {/* {!isReadonly && (
        <VisibilitySelector
          chatId={chatId}
          selectedVisibilityType={selectedVisibilityType}
          className="order-1 md:order-3"
        />
      )} */}
    </header>
  );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
  return prevProps.selectedModelId === nextProps.selectedModelId;
});
