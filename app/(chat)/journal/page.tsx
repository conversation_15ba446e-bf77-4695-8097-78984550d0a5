import { JournalEditor } from '@/components/journal-editor';
import { generateUUID } from '@/lib/utils';
import { auth } from '../../(auth)/auth';

export default async function Page() {
  const id = generateUUID();
  const session = await auth();

  return (
    <JournalEditor
      key={id}
      id={id}
      initialContent=""
      initialTitle=""
      isReadonly={false}
      isUserTemporary={!session?.user?.id}
    />
  );
}
