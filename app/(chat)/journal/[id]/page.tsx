import { notFound } from 'next/navigation';

import { auth } from '@/app/(auth)/auth';
import { JournalEditor } from '@/components/journal-editor';
import { getDocumentById } from '@/lib/db/queries';
import { getTempUserId } from '@/app/actions/cookies';

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;
  const document = await getDocumentById({ id });

  if (!document) {
    notFound();
  }

  const session = await auth();
  const tempUserId = await getTempUserId();

  if (!session?.user && !tempUserId) {
    return notFound();
  }

  if (session?.user?.id !== document.userId && tempUserId !== document.userId) {
    return notFound();
  }

  return (
    <JournalEditor
      id={document.id}
      initialContent={document.content || ''}
      initialTitle={document.title}
      isReadonly={session?.user?.id !== document.userId && tempUserId !== document.userId}
      isUserTemporary={!session?.user?.id}
    />
  );
}
