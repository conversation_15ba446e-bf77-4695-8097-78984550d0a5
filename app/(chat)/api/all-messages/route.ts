import { auth } from '@/app/(auth)/auth';
import { getAllMessagesByUserId } from '@/lib/db/queries';
import { convertToUIMessages } from '@/lib/utils';
import { cookies } from 'next/headers';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const limit = Number.parseInt(searchParams.get('limit') || '1000', 10);
  const offset = Number.parseInt(searchParams.get('offset') || '0', 10);

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.id && !tempUserId) {
    return Response.json('Unauthorized!', { status: 401 });
  }

  const userId = session?.user?.id || tempUserId!;

  try {
    // Get all messages for the user across all chats
    const messagesFromDb = await getAllMessagesByUserId({
      userId,
      limit,
      offset,
      orderBy: 'asc', // Order chronologically for single conversation view
    });

    const uiMessages = convertToUIMessages(messagesFromDb);

    return new Response(JSON.stringify(uiMessages), {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('Failed to get all messages for user:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
