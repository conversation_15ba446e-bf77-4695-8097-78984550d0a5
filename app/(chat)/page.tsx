import { Chat } from '@/components/chat';
import { DEFAULT_MODEL_NAME } from '@/lib/ai/models';
import { generateUUID, convertToUIMessages } from '@/lib/utils';
import { auth } from '../(auth)/auth';
import { getModelId } from '../actions/cookies';
import { getAllMessagesByUserId } from '@/lib/db/queries';
import { getTempUserId } from '../actions/cookies';

export default async function Page() {
  // Use a consistent single chat ID for all conversations
  const id = 'single-conversation';

  const modelIdFromCookie = await getModelId();

  const selectedModelId = DEFAULT_MODEL_NAME;

  const session = await auth();
  const tempUserId = await getTempUserId();

  // Get all messages for the user across all chats
  let initialMessages = [];
  const userId = session?.user?.id || tempUserId;

  if (userId) {
    try {
      const messagesFromDb = await getAllMessagesByUserId({
        userId,
        limit: 1000,
        offset: 0,
        orderBy: 'asc',
      });
      initialMessages = convertToUIMessages(messagesFromDb);
    } catch (error) {
      console.error('Failed to load user messages:', error);
      // Continue with empty messages if there's an error
    }
  }

  return (
    <Chat
      key={id}
      id={id}
      initialMessages={initialMessages}
      selectedModelId={selectedModelId}
      selectedVisibilityType="private"
      isReadonly={false}
      isUserTemporary={!session?.user?.id}
    />
  );
}
