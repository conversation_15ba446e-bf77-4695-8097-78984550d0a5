import { Chat } from '@/components/chat';
import { DEFAULT_MODEL_NAME, } from '@/lib/ai/models';
import { generateUUID } from '@/lib/utils';
import { auth } from '../(auth)/auth';
import { getModelId } from '../actions/cookies';

export default async function Page() {
  const id = generateUUID();

  const modelIdFromCookie = await getModelId();

  const selectedModelId =
    // models.find((model) => model.id === modelIdFromCookie)?.id ||
    DEFAULT_MODEL_NAME;

  const session = await auth();

  return (
    <Chat
      key={id}
      id={id}
      initialMessages={[]}
      selectedModelId={selectedModelId}
      selectedVisibilityType="private"
      isReadonly={false}
      isUserTemporary={!session?.user?.id}
    />
  );
}
